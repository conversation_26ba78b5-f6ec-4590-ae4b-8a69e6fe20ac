import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui' as ui;

class DebugFirebaseConfig {
  /// Configure Firebase for debug mode to minimize warnings
  static void configureForDebug() {
    try {
      // Set Firebase Auth language code to device locale
      final locale = ui.PlatformDispatcher.instance.locale;
      FirebaseAuth.instance.setLanguageCode(locale.languageCode);
      
      if (kDebugMode) {
        print('Debug Firebase locale set to: ${locale.languageCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Debug Firebase locale configuration failed: $e');
      }
    }
  }
  
  /// Disable problematic Firebase features in debug mode
  static void disableProblematicFeatures() {
    try {
      // In debug mode, we can disable certain features that cause warnings
      if (kDebugMode) {
        print('Debug mode: Disabling problematic Firebase features');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to disable problematic features: $e');
      }
    }
  }
}
