import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:ui' as ui;
import 'debug_firebase_config.dart';

class FirebaseConfig {
  /// Initialize Firebase App Check to reduce warnings and improve security
  static Future<void> initializeAppCheck() async {
    try {
      // In debug mode, we'll skip App Check to avoid attestation errors
      if (kDebugMode) {
        if (kDebugMode) {
          print(
              'Skipping Firebase App Check in debug mode to avoid attestation errors');
        }
        return;
      }

      // Only initialize App Check in release mode
      await FirebaseAppCheck.instance.activate(
        webProvider: ReCaptchaV3Provider('your-recaptcha-site-key'),
        androidProvider: AndroidProvider.playIntegrity,
        appleProvider: AppleProvider.deviceCheck,
      );

      if (kDebugMode) {
        print('Firebase App Check initialized successfully');
      }
    } catch (e) {
      // App Check is optional, so we don't want to crash the app if it fails
      if (kDebugMode) {
        print('Firebase App Check initialization failed: $e');
      }
    }
  }

  /// Set Firebase locale to reduce locale warnings
  static void configureFirebaseLocale() {
    if (kDebugMode) {
      // Use debug configuration in debug mode
      DebugFirebaseConfig.configureForDebug();
      DebugFirebaseConfig.disableProblematicFeatures();
      return;
    }

    try {
      // Set Firebase Auth language code to device locale
      final locale = ui.PlatformDispatcher.instance.locale;
      FirebaseAuth.instance.setLanguageCode(locale.languageCode);

      if (kDebugMode) {
        print('Firebase locale set to: ${locale.languageCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Firebase locale configuration failed: $e');
      }
    }
  }

  /// Configure Firebase Auth settings to reduce warnings
  static Future<void> configureFirebaseAuth() async {
    try {
      // Set additional Firebase Auth settings
      FirebaseAuth.instance.authStateChanges().listen((User? user) {
        if (kDebugMode) {
          print('Firebase Auth state changed: ${user?.email ?? 'No user'}');
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('Firebase Auth configuration failed: $e');
      }
    }
  }
}
