# Test Case - Chứ<PERSON> năng chọn ngày trong đặt vé

## Vấn đề hiện tại:
Chức năng chọn ngày trong phần đặt vé vẫn chưa dùng được

## Các thay đổi đã thực hiện:

### 1. ✅ Cập nhật BookingController
- Thêm parameter `selectedDate` cho `setSelectedTheater()`
- Thêm debug logging trong `loadShowtimes()`
- Sửa logic để truyền date khi load showtimes

### 2. ✅ Cập nhật MovieBookingPage
- Sửa logic chọn theater để truyền selectedDate
- Thêm debug logging để track date selection
- Force reload showtimes khi chọn ngày mới
- <PERSON><PERSON>i thiện auto-select theater logic

### 3. ✅ Cập nhật ShowtimeService
- Đã có sẵn method `getShowtimesByMovieTheaterAndDate()`

## Test Steps để kiểm tra:

### Test 1: <PERSON><PERSON><PERSON> tra console logs
1. Mở trang đặt vé cho một bộ phim
2. Mở Developer Console (F12)
3. <PERSON><PERSON><PERSON> các ngày khác nhau
4. Kiểm tra logs:
   - "Date selected: YYYY-MM-DD"
   - "Loading showtimes for movie=X, theater=Y, date=Z"
   - "BookingController.loadShowtimes: movieId=X, theaterId=Y, date=Z"
   - "Loading showtimes with date filter"
   - "Found X showtimes"

### Test 2: Kiểm tra UI response
1. Chọn ngày đầu tiên → Xem lịch chiếu
2. Chọn ngày thứ hai → Lịch chiếu có thay đổi không?
3. Chọn ngày thứ ba → Lịch chiếu có cập nhật không?

### Test 3: Kiểm tra data
1. Chọn ngày có lịch chiếu → Hiển thị showtimes
2. Chọn ngày không có lịch chiếu → Hiển thị "Không có lịch chiếu"
3. Chọn rạp khác → Lịch chiếu cập nhật theo rạp và ngày

## Possible Issues & Solutions:

### Issue 1: Không có dữ liệu showtimes
**Giải pháp:** Kiểm tra database có showtimes cho movie/theater/date không

### Issue 2: Date format không đúng
**Giải pháp:** Đảm bảo format "YYYY-MM-DD" consistent

### Issue 3: Theater chưa được chọn
**Giải pháp:** Auto-select theater đầu tiên khi load page

### Issue 4: Reactive update không hoạt động
**Giải pháp:** Force reload showtimes thay vì dựa vào reactive

## Debug Commands:
```dart
// In console, check current state:
print('Selected date: ${_selectedDate.value}');
print('Selected theater: ${_bookingController.selectedTheater.value?.name}');
print('Showtimes count: ${_bookingController.showtimes.length}');
```

## Expected Behavior:
- ✅ Chọn ngày → UI highlight ngày đã chọn
- ✅ Chọn ngày → Load showtimes cho ngày đó
- ✅ Chọn ngày khác → Lịch chiếu cập nhật
- ✅ Console logs hiển thị đúng thông tin
- ✅ Error handling khi không có lịch chiếu

## Files Modified:
- `lib/controllers/booking_controller.dart` - Added selectedDate parameter
- `lib/view/user/movie_booking_page.dart` - Fixed date selection logic
- `lib/services/showtime_service.dart` - Already has required method

## Next Steps if still not working:
1. Check Firestore data structure
2. Verify date format consistency
3. Test with sample data
4. Check network connectivity
5. Verify Firebase rules
