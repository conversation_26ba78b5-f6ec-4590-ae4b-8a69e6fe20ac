import 'package:get/get.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/showtime_model.dart';
import '../models/screen_model.dart';
import '../models/ticket_model.dart';
import '../services/theater_service.dart';
import '../services/showtime_service.dart';
import '../services/screen_service.dart';

class BookingController extends GetxController {
  final TheaterService _theaterService = TheaterService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final ScreenService _screenService = ScreenService();

  // Observable variables
  final RxList<TheaterModel> theaters = <TheaterModel>[].obs;
  final RxList<ShowtimeModel> showtimes = <ShowtimeModel>[].obs;
  final Rx<ScreenModel?> selectedScreen = Rx<ScreenModel?>(null);
  final RxList<String> selectedSeats = <String>[].obs;
  final RxDouble totalPrice = 0.0.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Selected items
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null);
  final Rx<TheaterModel?> selectedTheater = Rx<TheaterModel?>(null);
  final Rx<ShowtimeModel?> selectedShowtime = Rx<ShowtimeModel?>(null);

  @override
  void onInit() {
    super.onInit();
    loadTheaters();
  }

  // Load all theaters
  Future<void> loadTheaters({bool activeOnly = true}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final theaterList =
          await _theaterService.getAllTheaters(activeOnly: activeOnly);
      theaters.value = theaterList;
    } catch (e) {
      errorMessage.value = 'Không thể tải danh sách rạp: $e';
    } finally {
      isLoading.value = false;
    }
  }

  // Load showtimes for selected movie and theater
  Future<void> loadShowtimes(int movieId, String theaterId,
      {String? date}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      print(
          'BookingController.loadShowtimes: movieId=$movieId, theaterId=$theaterId, date=$date');

      List<ShowtimeModel> showtimeList;
      if (date != null && date.isNotEmpty) {
        print('Loading showtimes with date filter');
        showtimeList = await _showtimeService.getShowtimesByMovieTheaterAndDate(
            movieId, theaterId, date);
      } else {
        print('Loading showtimes without date filter');
        showtimeList = await _showtimeService.getShowtimesByMovieAndTheater(
            movieId, theaterId);
      }

      print('Found ${showtimeList.length} showtimes');
      showtimes.value = showtimeList;
    } catch (e) {
      print('Error loading showtimes: $e');
      errorMessage.value = 'Không thể tải lịch chiếu: $e';
      showtimes.clear();
    } finally {
      isLoading.value = false;
    }
  }

  // Load screen details
  Future<void> loadScreen(String screenId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final screen = await _screenService.getScreenById(screenId);
      selectedScreen.value = screen;
    } catch (e) {
      errorMessage.value = 'Không thể tải thông tin phòng chiếu: $e';
      selectedScreen.value = null;
    } finally {
      isLoading.value = false;
    }
  }

  // Set selected movie
  void setSelectedMovie(Movie movie) {
    selectedMovie.value = movie;
    // Clear previous selections
    selectedTheater.value = null;
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
  }

  // Set selected theater
  void setSelectedTheater(TheaterModel theater, {String? selectedDate}) {
    selectedTheater.value = theater;
    // Clear dependent selections
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
    errorMessage.value = ''; // Clear previous errors

    print('setSelectedTheater: ${theater.name}, selectedDate: $selectedDate');

    // Load showtimes for this theater and movie
    if (selectedMovie.value != null) {
      loadShowtimes(selectedMovie.value!.id, theater.id, date: selectedDate);
    }
  }

  // Set selected showtime
  void setSelectedShowtime(ShowtimeModel showtime) {
    selectedShowtime.value = showtime;
    // Clear seat selections
    selectedSeats.clear();
    totalPrice.value = 0.0;

    // Load screen details
    loadScreen(showtime.screenId);
  }

  // Toggle seat selection
  void toggleSeat(String seatId) {
    if (selectedSeats.contains(seatId)) {
      selectedSeats.remove(seatId);
    } else {
      selectedSeats.add(seatId);
    }
    calculateTotalPrice();
  }

  // Calculate total price based on selected seats
  void calculateTotalPrice() {
    if (selectedShowtime.value == null || selectedSeats.isEmpty) {
      totalPrice.value = 0.0;
      return;
    }

    double total = 0.0;
    final showtime = selectedShowtime.value!;

    for (String seatId in selectedSeats) {
      // Determine seat type based on seat ID (simplified logic)
      String seatType = 'standard';
      if (seatId.startsWith('V')) {
        seatType = 'vip';
      } else if (seatId.startsWith('P')) {
        seatType = 'premium';
      }

      total += showtime.pricing.getPriceForSeatType(seatType);
    }

    totalPrice.value = total;
  }

  // Check if seat is available
  bool isSeatAvailable(String seatId) {
    if (selectedShowtime.value == null) return false;

    final showtime = selectedShowtime.value!;
    return !showtime.bookedSeats.contains(seatId) &&
        !showtime.reservedSeats.contains(seatId);
  }

  // Check if seat is selected
  bool isSeatSelected(String seatId) {
    return selectedSeats.contains(seatId);
  }

  // Get booking summary
  Map<String, dynamic> getBookingSummary() {
    return {
      'movie': selectedMovie.value,
      'theater': selectedTheater.value,
      'showtime': selectedShowtime.value,
      'screen': selectedScreen.value,
      'seats': selectedSeats.toList(),
      'totalPrice': totalPrice.value,
      'seatCount': selectedSeats.length,
    };
  }

  // Validate booking
  bool isBookingValid() {
    return selectedMovie.value != null &&
        selectedTheater.value != null &&
        selectedShowtime.value != null &&
        selectedScreen.value != null &&
        selectedSeats.isNotEmpty;
  }

  // Clear all selections
  void clearBooking() {
    selectedMovie.value = null;
    selectedTheater.value = null;
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
    errorMessage.value = '';
  }

  // Get available dates for selected movie and theater
  List<String> getAvailableDates() {
    if (showtimes.isEmpty) return [];

    final dates = showtimes.map((showtime) => showtime.date).toSet().toList();
    dates.sort();
    return dates;
  }

  // Get showtimes for specific date
  List<ShowtimeModel> getShowtimesForDate(String date) {
    return showtimes.where((showtime) => showtime.date == date).toList();
  }

  // Format price
  String formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        )} VNĐ';
  }

  // Get seat type display name
  String getSeatTypeDisplayName(String seatType) {
    switch (seatType) {
      case 'vip':
        return 'VIP';
      case 'premium':
        return 'Premium';
      case 'standard':
        return 'Thường';
      default:
        return 'Thường';
    }
  }
}
