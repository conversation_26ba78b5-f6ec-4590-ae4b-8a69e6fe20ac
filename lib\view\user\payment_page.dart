import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../models/movie_model.dart';
import '../../models/theater_model.dart';
import '../../models/showtime_model.dart';
import '../../models/screen_model.dart';
import '../../models/ticket_model.dart';
import '../../models/payment_model.dart';
import '../../services/ticket_service.dart';
import '../../services/payment_service.dart';
import '../../services/showtime_service.dart';
import 'booking_success_page.dart';

class PaymentPage extends StatefulWidget {
  final Movie movie;
  final ShowtimeModel showtime;
  final TheaterModel theater;
  final ScreenModel screen;
  final List<String> selectedSeats;
  final double totalPrice;

  const PaymentPage({
    Key? key,
    required this.movie,
    required this.showtime,
    required this.theater,
    required this.screen,
    required this.selectedSeats,
    required this.totalPrice,
  }) : super(key: key);

  @override
  State<PaymentPage> createState() => _PaymentPageState();
}

class _PaymentPageState extends State<PaymentPage> {
  final TicketService _ticketService = TicketService();
  final PaymentService _paymentService = PaymentService();
  final ShowtimeService _showtimeService = ShowtimeService();

  final RxBool _isProcessing = false.obs;
  final Rx<PaymentMethod> _selectedPaymentMethod = PaymentMethod.momo.obs;
  final RxDouble _discountAmount = 0.0.obs;
  final RxDouble _finalPrice = 0.0.obs;

  @override
  void initState() {
    super.initState();
    _finalPrice.value = widget.totalPrice;
  }

  Future<void> _processPayment() async {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) {
      Get.snackbar(
        'Lỗi',
        'Vui lòng đăng nhập để tiếp tục',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
      );
      return;
    }

    try {
      _isProcessing.value = true;

      // Create ticket seats
      final ticketSeats = widget.selectedSeats.map((seatId) {
        final row = seatId.substring(0, 1);
        final number = seatId.substring(1);

        // Find seat type and price
        SeatType seatType = SeatType.standard;
        double seatPrice = widget.showtime.pricing.standard;

        for (final screenRow in widget.screen.seatLayout) {
          if (screenRow.row == row) {
            for (final seat in screenRow.seats) {
              if (seat.number == number) {
                seatType = seat.type;
                seatPrice =
                    widget.showtime.pricing.getPriceForSeatType(seatType.name);
                break;
              }
            }
            break;
          }
        }

        return TicketSeat(
          row: row,
          number: number,
          type: seatType.name,
          price: seatPrice,
        );
      }).toList();

      // Generate booking code
      final bookingCode = 'BK${DateTime.now().millisecondsSinceEpoch}';

      // Create ticket
      final ticket = Ticket(
        id: '', // Will be set by Firestore
        userId: user.uid,
        movieId: widget.movie.id,
        movieTitle: widget.movie.title,
        moviePosterPath: widget.movie.posterPath,
        theaterId: widget.theater.id,
        theaterName: widget.theater.name,
        screenId: widget.screen.id,
        screenName: widget.screen.name,
        showtimeId: widget.showtime.id,
        date: widget.showtime.date,
        time: widget.showtime.time,
        seats: ticketSeats,
        totalPrice: widget.totalPrice,
        discountApplied: _discountAmount.value,
        finalPrice: _finalPrice.value,
        paymentMethod: _selectedPaymentMethod.value.name,
        bookingCode: bookingCode,
        status: TicketStatus.confirmed,
        purchaseDate: DateTime.now(),
        loyaltyPointsEarned:
            (_finalPrice.value / 1000).round(), // 1 point per 1000 VND
      );

      // Save ticket
      final savedTicket = await _ticketService.createTicket(ticket);

      // Create payment
      final payment = PaymentModel(
        id: '', // Will be set by Firestore
        userId: user.uid,
        ticketId: savedTicket.id,
        amount: _finalPrice.value,
        method: _selectedPaymentMethod.value,
        provider: _selectedPaymentMethod.value.provider,
        status: PaymentStatus.pending,
        createdAt: DateTime.now(),
      );

      final savedPayment = await _paymentService.createPayment(payment);

      // Process payment
      final paymentResult = await _paymentService.processPayment(savedPayment);

      if (paymentResult['success'] == true) {
        // Book seats in showtime
        await _showtimeService.bookSeats(
            widget.showtime.id, widget.selectedSeats);

        // Navigate to success page
        Get.offAll(() => BookingSuccessPage(
              ticket: savedTicket.copyWith(
                paymentId: savedPayment.id,
                qrCode: 'QR_${savedTicket.bookingCode}',
              ),
              paymentResult: paymentResult,
            ));
      } else {
        // Update ticket status to failed
        await _ticketService.updateTicketStatus(
            savedTicket.id, TicketStatus.cancelled);

        // Release reserved seats
        await _showtimeService.releaseSeats(
            widget.showtime.id, widget.selectedSeats);

        Get.snackbar(
          'Thanh toán thất bại',
          paymentResult['message'] ??
              'Giao dịch không thành công. Vui lòng thử lại.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red.withOpacity(0.7),
          colorText: Colors.white,
          duration: const Duration(seconds: 4),
        );
      }
    } catch (e) {
      // Release reserved seats on error
      try {
        await _showtimeService.releaseSeats(
            widget.showtime.id, widget.selectedSeats);
      } catch (releaseError) {
        print('Error releasing seats: $releaseError');
      }

      Get.snackbar(
        'Lỗi',
        'Không thể xử lý thanh toán: ${e.toString().replaceAll('Exception: ', '')}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.withOpacity(0.7),
        colorText: Colors.white,
        duration: const Duration(seconds: 4),
      );
    } finally {
      _isProcessing.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Thanh Toán',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Booking Summary
                      _buildBookingSummary(),
                      const SizedBox(height: 16),

                      // Payment Methods
                      _buildPaymentMethods(),
                      const SizedBox(height: 16),

                      // Price Breakdown
                      _buildPriceBreakdown(),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),

              // Bottom Bar
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  border: Border(
                    top: BorderSide(color: Colors.white.withOpacity(0.2)),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            'Tổng thanh toán',
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.7),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Obx(() => Text(
                                '${_finalPrice.value.toStringAsFixed(0)} VNĐ',
                                style: GoogleFonts.mulish(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              )),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Obx(() => ElevatedButton(
                          onPressed:
                              _isProcessing.value ? null : _processPayment,
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                _isProcessing.value ? Colors.grey : Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: _isProcessing.value ? 0 : 2,
                          ),
                          child: _isProcessing.value
                              ? Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Đang xử lý...',
                                      style: GoogleFonts.mulish(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                )
                              : Text(
                                  'Thanh toán',
                                  style: GoogleFonts.mulish(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        )),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBookingSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Thông tin đặt vé',
            style: GoogleFonts.mulish(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.movie.fullPosterPath,
                  width: 60,
                  height: 80,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    width: 60,
                    height: 80,
                    color: Colors.grey[300],
                    child: const Icon(Icons.movie, color: Colors.grey),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.movie.title,
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      widget.theater.name,
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.screen.name} • ${widget.screen.type.displayName}',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.showtime.date} • ${widget.showtime.time}',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Ghế: ${widget.selectedSeats.join(', ')}',
                      style: GoogleFonts.mulish(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.blue[200],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Phương thức thanh toán',
            style: GoogleFonts.mulish(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          ...PaymentMethod.values
              .map(
                (method) => Obx(() => RadioListTile<PaymentMethod>(
                      value: method,
                      groupValue: _selectedPaymentMethod.value,
                      onChanged: (value) {
                        if (value != null) {
                          _selectedPaymentMethod.value = value;
                        }
                      },
                      title: Row(
                        children: [
                          Icon(
                            _getPaymentMethodIcon(method),
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            method.displayName,
                            style: GoogleFonts.mulish(
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      activeColor: Colors.blue,
                      contentPadding: EdgeInsets.zero,
                    )),
              )
              .toList(),
        ],
      ),
    );
  }

  Widget _buildPriceBreakdown() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Chi tiết giá',
            style: GoogleFonts.mulish(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Giá vé (${widget.selectedSeats.length} ghế)',
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
              Text(
                '${widget.totalPrice.toStringAsFixed(0)} VNĐ',
                style: GoogleFonts.mulish(
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          if (_discountAmount.value > 0) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Giảm giá',
                  style: GoogleFonts.mulish(
                    fontSize: 14,
                    color: Colors.white.withOpacity(0.7),
                  ),
                ),
                Obx(() => Text(
                      '-${_discountAmount.value.toStringAsFixed(0)} VNĐ',
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        color: Colors.green,
                      ),
                    )),
              ],
            ),
          ],
          const Divider(color: Colors.white24, height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Tổng cộng',
                style: GoogleFonts.mulish(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Obx(() => Text(
                    '${_finalPrice.value.toStringAsFixed(0)} VNĐ',
                    style: GoogleFonts.mulish(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return Icons.credit_card;
      case PaymentMethod.momo:
        return Icons.account_balance_wallet;
      case PaymentMethod.zalopay:
        return Icons.payment;
      case PaymentMethod.vnpay:
        return Icons.account_balance;
      case PaymentMethod.cash:
        return Icons.money;
    }
  }
}
