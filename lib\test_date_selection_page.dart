import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'controllers/booking_controller.dart';
import 'models/movie_model.dart';

class TestDateSelectionPage extends StatefulWidget {
  const TestDateSelectionPage({Key? key}) : super(key: key);

  @override
  State<TestDateSelectionPage> createState() => _TestDateSelectionPageState();
}

class _TestDateSelectionPageState extends State<TestDateSelectionPage> {
  final BookingController _bookingController = Get.put(BookingController());
  final RxString _selectedDate = ''.obs;
  final RxList<String> _availableDates = <String>[].obs;

  // Test movie
  final Movie testMovie = Movie(
    id: 1,
    title: "Test Movie",
    overview: "Test movie for date selection",
    posterPath: "/test.jpg",
    backdropPath: "/test_backdrop.jpg",
    releaseDate: "2024-01-01",
    voteAverage: 8.5,
    voteCount: 1000,
    adult: false,
    originalLanguage: "en",
    originalTitle: "Test Movie",
    popularity: 100.0,
    video: false,
    genres: ["Action", "Drama"],
    runtime: 120,
    status: MovieStatus.nowPlaying,
    budget: 1000000,
    revenue: 5000000,
    homepage: "",
    imdbId: "tt1234567",
    tagline: "Test tagline",
    productionCompanies: [],
    productionCountries: [],
    spokenLanguages: [],
    belongsToCollection: null,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );

  @override
  void initState() {
    super.initState();
    _bookingController.setSelectedMovie(testMovie);
    _generateAvailableDates();
  }

  void _generateAvailableDates() {
    final dates = <String>[];
    final now = DateTime.now();

    for (int i = 0; i < 7; i++) {
      final date = now.add(Duration(days: i));
      final dateString =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      dates.add(dateString);
    }

    _availableDates.value = dates;
    _selectedDate.value = dates.first;
  }

  Future<void> _testLoadShowtimes() async {
    if (_bookingController.selectedTheater.value == null ||
        _selectedDate.value.isEmpty) {
      print('Cannot test: theater=${_bookingController.selectedTheater.value?.name}, date=${_selectedDate.value}');
      return;
    }

    print('Testing showtimes for date: ${_selectedDate.value}');
    await _bookingController.loadShowtimes(
      testMovie.id,
      _bookingController.selectedTheater.value!.id,
      date: _selectedDate.value,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Date Selection'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current State Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Current State:', style: GoogleFonts.mulish(fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 8),
                    Obx(() => Text('Selected Date: ${_selectedDate.value}')),
                    Obx(() => Text('Selected Theater: ${_bookingController.selectedTheater.value?.name ?? "None"}')),
                    Obx(() => Text('Theaters Count: ${_bookingController.theaters.length}')),
                    Obx(() => Text('Showtimes Count: ${_bookingController.showtimes.length}')),
                    Obx(() => Text('Loading: ${_bookingController.isLoading.value}')),
                    Obx(() => Text('Error: ${_bookingController.errorMessage.value}')),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Date Selection
            Text('Select Date:', style: GoogleFonts.mulish(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Obx(() => SizedBox(
              height: 60,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _availableDates.length,
                itemBuilder: (context, index) {
                  final dateString = _availableDates[index];
                  final date = DateTime.parse(dateString);
                  final isSelected = _selectedDate.value == dateString;

                  return GestureDetector(
                    onTap: () {
                      print('Date tapped: $dateString');
                      _selectedDate.value = dateString;
                      _testLoadShowtimes();
                    },
                    child: Container(
                      width: 80,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? Colors.blue : Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected ? Colors.blue : Colors.grey,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${date.day}',
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: isSelected ? Colors.white : Colors.black,
                            ),
                          ),
                          Text(
                            '${date.month}/${date.day}',
                            style: GoogleFonts.mulish(
                              fontSize: 10,
                              color: isSelected ? Colors.white : Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            )),
            
            const SizedBox(height: 16),
            
            // Theater Selection
            Text('Theaters:', style: GoogleFonts.mulish(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Obx(() => _bookingController.theaters.isEmpty 
              ? const CircularProgressIndicator()
              : Column(
                  children: _bookingController.theaters.map((theater) {
                    final isSelected = _bookingController.selectedTheater.value?.id == theater.id;
                    return ListTile(
                      title: Text(theater.name),
                      subtitle: Text(theater.address.city),
                      selected: isSelected,
                      onTap: () {
                        print('Theater selected: ${theater.name}');
                        _bookingController.setSelectedTheater(theater, selectedDate: _selectedDate.value);
                      },
                    );
                  }).toList(),
                )),
            
            const SizedBox(height: 16),
            
            // Showtimes
            Text('Showtimes:', style: GoogleFonts.mulish(fontSize: 16, fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Expanded(
              child: Obx(() {
                if (_bookingController.isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }
                
                if (_bookingController.errorMessage.value.isNotEmpty) {
                  return Center(
                    child: Text(
                      'Error: ${_bookingController.errorMessage.value}',
                      style: const TextStyle(color: Colors.red),
                    ),
                  );
                }
                
                if (_bookingController.showtimes.isEmpty) {
                  return const Center(child: Text('No showtimes found'));
                }
                
                return ListView.builder(
                  itemCount: _bookingController.showtimes.length,
                  itemBuilder: (context, index) {
                    final showtime = _bookingController.showtimes[index];
                    return ListTile(
                      title: Text('${showtime.time} - ${showtime.endTime}'),
                      subtitle: Text('Date: ${showtime.date}, Available: ${showtime.availableSeats}'),
                    );
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
