# Test Plan - Chứ<PERSON> năng đặt vé và thanh toán đã sửa

## Các lỗi đã sửa:

### 1. ✅ Lỗi chọn ngày
**Vấn đề cũ:** `_loadShowtimes()` không sử dụng ngày đã chọn
**Gi<PERSON>i pháp:**
- Thêm phương thức `getShowtimesByMovieTheaterAndDate` trong ShowtimeService
- Cập nhật `loadShowtimes` trong BookingController để nhận parameter `date`
- Sửa `_loadShowtimes()` trong MovieBookingPage để truyền ngày đã chọn

### 2. ✅ UI chọn rạp không hợp lý
**Vấn đề cũ:** Dropdown đơn giản, thiếu thông tin
**Giải pháp:**
- Thay thế dropdown bằng horizontal ListView với card design
- Hiển thị tên rạp, địa chỉ đầy đủ, và thành phố
- Thêm visual feedback khi chọn rạp
- Sử dụng `theater.address.fullAddress` và `theater.address.city`

### 3. ✅ Lỗi thanh toán
**Vấn đề cũ:** 
- Thiếu phương thức `_getPaymentMethodIcon` (đã có sẵn)
- Error handling kém
- Không release seats khi payment thất bại

**Giải pháp:**
- Cải thiện error handling với thông báo rõ ràng hơn
- Thêm phương thức `releaseSeats` trong ShowtimeService
- Tự động release reserved seats khi payment thất bại
- Cập nhật ticket status thành cancelled khi payment thất bại
- Cải thiện UI button với loading state

### 4. ✅ Cải tiến bổ sung
- Thêm error state với retry button trong showtimes list
- Auto-select first theater khi load page
- Cải thiện loading states và error messages
- Thêm validation và feedback tốt hơn

## Test Cases cần kiểm tra:

### Test Case 1: Chọn ngày
1. Mở trang đặt vé cho một bộ phim
2. Chọn các ngày khác nhau
3. Kiểm tra lịch chiếu cập nhật theo ngày đã chọn
4. Verify: Chỉ hiển thị showtimes cho ngày đã chọn

### Test Case 2: Chọn rạp
1. Scroll qua danh sách rạp (horizontal)
2. Chọn rạp khác nhau
3. Kiểm tra visual feedback (border, color change)
4. Verify: Lịch chiếu cập nhật theo rạp đã chọn

### Test Case 3: Thanh toán thành công
1. Chọn ngày, rạp, suất chiếu, ghế
2. Đi đến trang thanh toán
3. Chọn phương thức thanh toán
4. Nhấn "Thanh toán"
5. Verify: Chuyển đến trang success

### Test Case 4: Thanh toán thất bại
1. Thực hiện steps 1-4 như trên
2. Nếu payment thất bại (10% chance)
3. Verify: 
   - Hiển thị error message rõ ràng
   - Reserved seats được release
   - Ticket status = cancelled
   - Có thể thử lại

### Test Case 5: Error handling
1. Disconnect internet
2. Thử load showtimes
3. Verify: Hiển thị error với retry button
4. Reconnect và nhấn retry
5. Verify: Load thành công

## Files đã thay đổi:
- `lib/services/showtime_service.dart` - Thêm getShowtimesByMovieTheaterAndDate, releaseSeats
- `lib/controllers/booking_controller.dart` - Cập nhật loadShowtimes với date parameter
- `lib/view/user/movie_booking_page.dart` - UI cải tiến cho date/theater selection, error handling
- `lib/view/user/payment_page.dart` - Cải thiện error handling và loading states

## Kết quả mong đợi:
- ✅ Chọn ngày hoạt động chính xác
- ✅ UI chọn rạp đẹp và thông tin đầy đủ
- ✅ Thanh toán robust với error handling tốt
- ✅ UX mượt mà và feedback rõ ràng
