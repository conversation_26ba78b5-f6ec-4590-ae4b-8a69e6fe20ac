import 'package:get/get.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';
import '../services/controller_initializer.dart';
import '../utils/storage_cleanup.dart';
import '../utils/developer_mode.dart';

class AuthController extends GetxController {
  final AuthService _authService = AuthService();
  final StorageService _storageService = StorageService();

  final Rx<UserModel?> _user = Rx<UserModel?>(null);
  final RxBool _isLoading = false.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _errorMessage = ''.obs;

  UserModel? get user => _user.value;
  Rx<UserModel?> get userRx => _user; // Thêm getter cho userRx
  bool get isLoading => _isLoading.value;
  bool get isLoggedIn => _isLoggedIn.value;
  RxBool get isLoggedInObs => _isLoggedIn;
  String get errorMessage => _errorMessage.value;

  // Lấy vai trò của người dùng hiện tại
  String get userRole {
    // Kiểm tra xem người dùng có phải là developer không
    try {
      final developerMode = Get.find<DeveloperMode>();
      if (developerMode.isDeveloper()) {
        return 'developer';
      }
    } catch (e) {
      // Bỏ qua lỗi nếu DeveloperMode chưa được khởi tạo
    }

    // Kiểm tra vai trò từ UserModel
    if (_user.value?.isAdmin == true) {
      return 'admin';
    }

    return 'user';
  }

  // Check if current user is admin or developer
  bool get isAdmin {
    // Kiểm tra xem người dùng có phải là developer không
    try {
      final developerMode = Get.find<DeveloperMode>();
      if (developerMode.isDeveloper()) {
        return true; // Developer luôn có quyền admin
      }
    } catch (e) {
      // Bỏ qua lỗi nếu DeveloperMode chưa được khởi tạo
    }

    // Kiểm tra quyền admin thông thường
    return _user.value?.isAdmin ?? false;
  }

  @override
  void onInit() {
    super.onInit();
    checkLoginStatus();
  }

  Future<void> _forceClearAllData() async {
    try {
      await _storageService.clearAll();

      await StorageCleanup.clearAllData();
      await StorageCleanup.checkAndFixProblematicData();
    } catch (e) {
      // Ignore errors during cleanup
    }
  }

  Future<void> checkLoginStatus() async {
    _isLoading.value = true;
    try {
      _isLoggedIn.value = await _authService.isLoggedIn();
      if (_isLoggedIn.value) {
        try {
          _user.value = await _authService.getCurrentUser();

          // Controllers are already initialized in main.dart
        } catch (e) {
          if (e.toString().contains('PigeonUserDetails')) {
            await _forceClearAllData();
            _isLoggedIn.value = false;
            _user.value = null;
          } else {
            _errorMessage.value = _formatErrorMessage(e);
          }
        }
      }
    } catch (e) {
      await _forceClearAllData();
      _isLoggedIn.value = false;
      _errorMessage.value = _formatErrorMessage(e);
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> login(String email, String password) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      try {
        final user = await _authService.login(email, password);
        if (user != null) {
          _user.value = user;
          _isLoggedIn.value = true;
          return true;
        }
      } catch (firstAttemptError) {
        if (firstAttemptError.toString().contains('PigeonUserDetails')) {
          await _forceClearAllData();
        } else if (firstAttemptError
            .toString()
            .contains('cloud_firestore/permission-denied')) {
          // If we get a Firestore permission error, we can still proceed
          // The modified AuthService will handle this gracefully
        } else {
          rethrow;
        }
      }
      final user = await _authService.login(email, password);
      if (user != null) {
        _user.value = user;
        _isLoggedIn.value = true;

        // Controllers are already initialized in main.dart

        return true;
      } else {
        _errorMessage.value = 'Invalid email or password';
        return false;
      }
    } catch (e) {
      if (e.toString().contains('user-not-found')) {
        _errorMessage.value =
            'No user found with this email. Please register first.';
      } else if (e.toString().contains('wrong-password')) {
        _errorMessage.value = 'Incorrect password. Please try again.';
      } else if (e.toString().contains('too-many-requests')) {
        _errorMessage.value =
            'Too many failed login attempts. Please try again later.';
      } else if (e.toString().contains('network-request-failed')) {
        _errorMessage.value =
            'Network error. Please check your internet connection.';
      } else if (e.toString().contains('cloud_firestore/permission-denied')) {
        // If we get a Firestore permission error, we can still log in
        // The user data will be stored locally only
        _errorMessage.value =
            'Logged in with limited functionality. Some features may not be available.';
        return true;
      } else {
        _errorMessage.value = _formatErrorMessage(e);
      }
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<bool> register(String email, String password, String name) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      try {
        final user = await _authService.register(email, password, name);
        if (user != null) {
          _user.value = user;
          _isLoggedIn.value = true;
          return true;
        }
      } catch (firstAttemptError) {
        if (firstAttemptError.toString().contains('PigeonUserDetails')) {
          // Clear data and retry
          await _forceClearAllData();
        } else if (firstAttemptError
            .toString()
            .contains('cloud_firestore/permission-denied')) {
          // If we get a Firestore permission error, we can still proceed
          // The modified AuthService will handle this gracefully
        } else {
          rethrow;
        }
      }
      final user = await _authService.register(email, password, name);
      if (user != null) {
        _user.value = user;
        _isLoggedIn.value = true;

        // Controllers are already initialized in main.dart

        return true;
      } else {
        _errorMessage.value = 'Registration failed';
        return false;
      }
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        _errorMessage.value =
            'This email is already registered. Please login instead.';
      } else if (e.toString().contains('weak-password')) {
        _errorMessage.value =
            'Password is too weak. Please use a stronger password.';
      } else if (e.toString().contains('invalid-email')) {
        _errorMessage.value =
            'Invalid email format. Please enter a valid email.';
      } else if (e.toString().contains('network-request-failed')) {
        _errorMessage.value =
            'Network error. Please check your internet connection.';
      } else if (e.toString().contains('cloud_firestore/permission-denied')) {
        // If we get a Firestore permission error, we can still register
        // The user data will be stored locally only
        _errorMessage.value =
            'Registered with limited functionality. Some features may not be available.';
        return true;
      } else {
        _errorMessage.value = _formatErrorMessage(e);
      }
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  Future<void> logout() async {
    _isLoading.value = true;
    try {
      await _forceClearAllData();
      try {
        await _authService.logout();
      } catch (e) {
        // Ignore any errors during logout since we've already cleared data
      }

      // Controllers remain initialized

      _user.value = null;
      _isLoggedIn.value = false;
    } catch (e) {
      // Even if there's an error, still clear the local state
      _user.value = null;
      _isLoggedIn.value = false;
      _errorMessage.value = _formatErrorMessage(e);
    } finally {
      _isLoading.value = false;
    }
  }

  // Format error message to be more user-friendly
  String _formatErrorMessage(dynamic error) {
    String message = error.toString();

    // Extract message from Exception
    if (message.contains('Exception:')) {
      message = message.split('Exception:')[1].trim();
    }

    // Remove Firebase specific prefixes
    if (message.contains('[firebase_auth/')) {
      message = message.split(']')[1].trim();
    }

    return message;
  }

  // Clear error message
  void clearError() {
    _errorMessage.value = '';
  }

  // Diagnostic method to check Firebase auth state
  Future<String> checkFirebaseAuthState() async {
    try {
      final currentUser = _authService.getCurrentFirebaseUser();
      if (currentUser != null) {
        return 'Firebase user is logged in: ${currentUser.email}';
      } else {
        return 'No Firebase user is logged in';
      }
    } catch (e) {
      return 'Error checking Firebase auth state: $e';
    }
  }

  // Admin management methods

  // Check if a user is an admin
  Future<bool> isUserAdmin(String userId) async {
    try {
      return await _authService.isUserAdmin(userId);
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    }
  }

  // Check if a user is a developer
  Future<bool> isUserDeveloper(String userId) async {
    try {
      return await _authService.isUserDeveloper(userId);
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    }
  }

  // Set a user as admin
  Future<bool> setUserAsAdmin(String userId) async {
    _isLoading.value = true;
    try {
      final result = await _authService.setUserAsAdmin(userId);

      // If the current user is being updated, refresh the user data
      if (_user.value?.id == userId && result) {
        _user.value = _user.value?.copyWith(role: UserRole.admin);
      }

      return result;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Set a user as regular user
  Future<bool> setUserAsRegular(String userId) async {
    _isLoading.value = true;
    try {
      final result = await _authService.setUserAsRegular(userId);

      // If the current user is being updated, refresh the user data
      if (_user.value?.id == userId && result) {
        _user.value = _user.value?.copyWith(role: UserRole.user);
      }

      return result;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Set a user as developer
  Future<bool> setUserAsDeveloper(String userId) async {
    _isLoading.value = true;
    try {
      final result = await _authService.setUserAsDeveloper(userId);

      // If the current user is being updated, refresh the user data
      if (_user.value?.id == userId && result) {
        _user.value = _user.value?.copyWith(role: UserRole.developer);
      }

      return result;
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // Get all users
  Future<List<UserModel>> getAllUsers() async {
    _isLoading.value = true;
    try {
      return await _authService.getAllUsers();
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return [];
    } finally {
      _isLoading.value = false;
    }
  }

  // Lấy danh sách ID của tất cả admin và developer
  Future<List<String>> getAdminAndDeveloperIds() async {
    try {
      // Lấy danh sách người dùng
      final users = await getAllUsers();

      // Lọc ra các admin và developer từ danh sách người dùng
      // Không cần truy cập collection developer_accounts
      final adminAndDevIds = users
          .where((user) =>
              user.isAdmin) // isAdmin trả về true cho cả admin và developer
          .map((user) => user.id ?? '')
          .where((id) => id.isNotEmpty)
          .toList();

      return adminAndDevIds;
    } catch (e) {
      print('Error getting admin and developer IDs: $e');
      return [];
    }
  }

  // Update user profile
  Future<bool> updateProfile({String? name, String? photoUrl}) async {
    _isLoading.value = true;
    _errorMessage.value = '';

    try {
      final userId = _user.value?.id;
      if (userId == null) {
        _errorMessage.value = 'User not logged in';
        return false;
      }

      final result = await _authService.updateUserProfile(
        userId,
        name: name,
        photoUrl: photoUrl,
      );

      if (result) {
        // Update the local user object
        final updatedUser = _user.value?.copyWith(
          name: name ?? _user.value?.name,
          photoUrl: photoUrl ?? _user.value?.photoUrl,
        );

        if (updatedUser != null) {
          _user.value = updatedUser;
        }

        return true;
      } else {
        _errorMessage.value = 'Failed to update profile';
        return false;
      }
    } catch (e) {
      _errorMessage.value = _formatErrorMessage(e);
      return false;
    } finally {
      _isLoading.value = false;
    }
  }

  // This method was moved to the top of the file
}
