import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/movie_model.dart';
import '../../models/theater_model.dart';
import '../../models/showtime_model.dart';
import '../../controllers/booking_controller.dart';
import 'seat_selection_page.dart';

class MovieBookingPage extends StatefulWidget {
  final Movie movie;

  const MovieBookingPage({Key? key, required this.movie}) : super(key: key);

  @override
  State<MovieBookingPage> createState() => _MovieBookingPageState();
}

class _MovieBookingPageState extends State<MovieBookingPage> {
  final BookingController _bookingController = Get.put(BookingController());
  final RxString _selectedDate = ''.obs;
  final RxList<String> _availableDates = <String>[].obs;

  @override
  void initState() {
    super.initState();
    _bookingController.setSelectedMovie(widget.movie);
    _generateAvailableDates();

    // Load showtimes for first date and first theater if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Listen to theaters changes to auto-select first theater
      ever(_bookingController.theaters, (List<TheaterModel> theaters) {
        if (theaters.isNotEmpty &&
            _bookingController.selectedTheater.value == null &&
            _selectedDate.value.isNotEmpty) {
          _bookingController.setSelectedTheater(theaters.first,
              selectedDate: _selectedDate.value);
        }
      });
    });
  }

  void _generateAvailableDates() {
    final dates = <String>[];
    final now = DateTime.now();

    for (int i = 0; i < 7; i++) {
      final date = now.add(Duration(days: i));
      final dateString =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      dates.add(dateString);
    }

    _availableDates.value = dates;
    _selectedDate.value = dates.first;
  }

  Future<void> _loadShowtimes() async {
    if (_bookingController.selectedTheater.value == null ||
        _selectedDate.value.isEmpty) {
      print(
          'Cannot load showtimes: theater=${_bookingController.selectedTheater.value?.name}, date=${_selectedDate.value}');
      return;
    }

    print(
        'Loading showtimes for movie=${widget.movie.id}, theater=${_bookingController.selectedTheater.value!.name}, date=${_selectedDate.value}');
    await _bookingController.loadShowtimes(
      widget.movie.id,
      _bookingController.selectedTheater.value!.id,
      date: _selectedDate.value,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // App Bar
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                    ),
                    Expanded(
                      child: Text(
                        'Đặt Vé',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Movie Info
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.white.withOpacity(0.2)),
                ),
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        widget.movie.fullPosterPath,
                        width: 60,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          width: 60,
                          height: 80,
                          color: Colors.grey[300],
                          child: const Icon(Icons.movie, color: Colors.grey),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.movie.title,
                            style: GoogleFonts.mulish(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          if (widget.movie.genres.isNotEmpty)
                            Text(
                              widget.movie.genres.join(', '),
                              style: GoogleFonts.mulish(
                                fontSize: 12,
                                color: Colors.white.withOpacity(0.7),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              const Icon(Icons.star,
                                  color: Colors.amber, size: 16),
                              const SizedBox(width: 4),
                              Text(
                                widget.movie.voteAverage?.toStringAsFixed(1) ??
                                    'N/A',
                                style: GoogleFonts.mulish(
                                  fontSize: 12,
                                  color: Colors.white.withOpacity(0.7),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Date Selection
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chọn ngày',
                      style: GoogleFonts.mulish(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => SizedBox(
                          height: 60,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _availableDates.length,
                            itemBuilder: (context, index) {
                              final dateString = _availableDates[index];
                              final date = DateTime.parse(dateString);
                              final isSelected =
                                  _selectedDate.value == dateString;

                              return GestureDetector(
                                onTap: () {
                                  print('Date selected: $dateString');
                                  _selectedDate.value = dateString;

                                  // Force reload showtimes with new date
                                  if (_bookingController
                                          .selectedTheater.value !=
                                      null) {
                                    _bookingController.loadShowtimes(
                                      widget.movie.id,
                                      _bookingController
                                          .selectedTheater.value!.id,
                                      date: dateString,
                                    );
                                  }
                                },
                                child: Container(
                                  width: 80,
                                  margin: const EdgeInsets.only(right: 8),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.blue.withOpacity(0.3)
                                        : Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isSelected
                                          ? Colors.blue
                                          : Colors.white.withOpacity(0.2),
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        _getDayName(date.weekday),
                                        style: GoogleFonts.mulish(
                                          fontSize: 12,
                                          color: isSelected
                                              ? Colors.blue[200]
                                              : Colors.white.withOpacity(0.7),
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        date.day.toString(),
                                        style: GoogleFonts.mulish(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: isSelected
                                              ? Colors.white
                                              : Colors.white.withOpacity(0.8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        )),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Theater Selection
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chọn rạp',
                      style: GoogleFonts.mulish(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Obx(() => SizedBox(
                          height: 120,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _bookingController.theaters.length,
                            itemBuilder: (context, index) {
                              final theater =
                                  _bookingController.theaters[index];
                              final isSelected = _bookingController
                                      .selectedTheater.value?.id ==
                                  theater.id;

                              return GestureDetector(
                                onTap: () {
                                  _bookingController.setSelectedTheater(theater,
                                      selectedDate: _selectedDate.value);
                                },
                                child: Container(
                                  width: 200,
                                  margin: const EdgeInsets.only(right: 12),
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Colors.blue.withOpacity(0.3)
                                        : Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: isSelected
                                          ? Colors.blue
                                          : Colors.white.withOpacity(0.2),
                                      width: isSelected ? 2 : 1,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        theater.name,
                                        style: GoogleFonts.mulish(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: isSelected
                                              ? Colors.white
                                              : Colors.white.withOpacity(0.9),
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        theater.address.fullAddress,
                                        style: GoogleFonts.mulish(
                                          fontSize: 11,
                                          color: isSelected
                                              ? Colors.blue[200]
                                              : Colors.white.withOpacity(0.6),
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const Spacer(),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.location_on,
                                            size: 12,
                                            color: isSelected
                                                ? Colors.blue[200]
                                                : Colors.white.withOpacity(0.6),
                                          ),
                                          const SizedBox(width: 4),
                                          Expanded(
                                            child: Text(
                                              theater.address.city,
                                              style: GoogleFonts.mulish(
                                                fontSize: 10,
                                                color: isSelected
                                                    ? Colors.blue[200]
                                                    : Colors.white
                                                        .withOpacity(0.6),
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        )),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Showtimes
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lịch chiếu',
                        style: GoogleFonts.mulish(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: Obx(() {
                          if (_bookingController.isLoading.value) {
                            return const Center(
                              child: CircularProgressIndicator(
                                  color: Colors.white),
                            );
                          }

                          if (_bookingController
                              .errorMessage.value.isNotEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.error_outline,
                                    size: 64,
                                    color: Colors.red.withOpacity(0.7),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    _bookingController.errorMessage.value,
                                    style: GoogleFonts.mulish(
                                      fontSize: 14,
                                      color: Colors.red.withOpacity(0.8),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 16),
                                  ElevatedButton(
                                    onPressed: _loadShowtimes,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue,
                                      foregroundColor: Colors.white,
                                    ),
                                    child: Text(
                                      'Thử lại',
                                      style: GoogleFonts.mulish(),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }

                          if (_bookingController.showtimes.isEmpty) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.schedule,
                                    size: 64,
                                    color: Colors.white.withOpacity(0.5),
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Không có lịch chiếu',
                                    style: GoogleFonts.mulish(
                                      fontSize: 18,
                                      color: Colors.white.withOpacity(0.7),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    'Vui lòng chọn ngày khác hoặc rạp khác',
                                    style: GoogleFonts.mulish(
                                      fontSize: 14,
                                      color: Colors.white.withOpacity(0.5),
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            );
                          }

                          return GridView.builder(
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              childAspectRatio: 2.5,
                              crossAxisSpacing: 8,
                              mainAxisSpacing: 8,
                            ),
                            itemCount: _bookingController.showtimes.length,
                            itemBuilder: (context, index) {
                              final showtime =
                                  _bookingController.showtimes[index];
                              return _buildShowtimeCard(showtime);
                            },
                          );
                        }),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShowtimeCard(ShowtimeModel showtime) {
    final isBookable = showtime.isBookable;

    return GestureDetector(
      onTap: isBookable
          ? () {
              Get.to(() => SeatSelectionPage(
                    movie: widget.movie,
                    showtime: showtime,
                    theater: _bookingController.selectedTheater.value!,
                  ));
            }
          : null,
      child: Container(
        decoration: BoxDecoration(
          color: isBookable
              ? Colors.white.withOpacity(0.1)
              : Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isBookable
                ? Colors.white.withOpacity(0.2)
                : Colors.grey.withOpacity(0.2),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              showtime.time,
              style: GoogleFonts.mulish(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color:
                    isBookable ? Colors.white : Colors.white.withOpacity(0.5),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '${showtime.availableSeats} ghế',
              style: GoogleFonts.mulish(
                fontSize: 10,
                color: isBookable
                    ? Colors.white.withOpacity(0.7)
                    : Colors.white.withOpacity(0.3),
              ),
            ),
            if (!isBookable) ...[
              const SizedBox(height: 2),
              Text(
                showtime.isFull ? 'Hết vé' : 'Đã hủy',
                style: GoogleFonts.mulish(
                  fontSize: 8,
                  color: Colors.red.withOpacity(0.7),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1:
        return 'T2';
      case 2:
        return 'T3';
      case 3:
        return 'T4';
      case 4:
        return 'T5';
      case 5:
        return 'T6';
      case 6:
        return 'T7';
      case 7:
        return 'CN';
      default:
        return '';
    }
  }
}
